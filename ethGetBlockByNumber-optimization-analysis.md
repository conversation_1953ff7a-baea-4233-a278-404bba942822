# ethGetBlockByNumber Optimization Analysis

## Executive Summary

The current system's `doSubscribe` function calls `ethGetBlockByNumber(parameter, true)` which retrieves full transaction objects, but the downstream processing only requires transaction hashes. This results in unnecessary data transfer and processing overhead that can be optimized by setting the second parameter to `false`.

## Current Implementation Analysis

### 1. Problem Location

**File**: `bcclient-base/src/main/java/com/decurret_dcp/dcjpy/bcclient/base/websocket/WebSocketConnectionPoolBase.java`

**Method**: `doSubscribe(NewHeadsNotification notification)`

**Line**: 162

```java
private void doSubscribe(NewHeadsNotification notification) {
    NewHead newHead = notification.getParams().getResult();
    BigInteger blockNumber = Numeric.toBigInt(newHead.getNumber());
    DefaultBlockParameter parameter = DefaultBlockParameter.valueOf(blockNumber);

    // ブロックデータ取得
    EthBlock ethBlock;
    try {
        ethBlock = this.callerWeb3j.ethGetBlockByNumber(parameter, true).send(); // ← ISSUE: true retrieves full tx objects
    } catch (IOException ioExc) {
        log.error("GetBlockByNumber failed.", ioExc);
        throw new BlockchainIOException("ethGetBlockByNumber.send() failed", ioExc);
    }

    this.handler.onEvent(this, ethBlock);
}
```

### 2. Data Flow Analysis

#### Current Flow with Full Transaction Objects
```
1. doSubscribe() calls ethGetBlockByNumber(parameter, true)
   ↓ [Retrieves full transaction objects with all fields]
2. handler.onEvent() receives EthBlock with full transaction data
   ↓ [Passes complete transaction objects]
3. Event handlers process transactions
   ↓ [Only extracts transaction hash]
4. Only transactionObj.getHash() is used
```

#### Optimized Flow with Transaction Hashes Only
```
1. doSubscribe() calls ethGetBlockByNumber(parameter, false)
   ↓ [Retrieves only transaction hashes]
2. handler.onEvent() receives EthBlock with transaction hashes
   ↓ [Passes lightweight transaction references]
3. Event handlers process transactions
   ↓ [Uses transaction hash directly]
4. transactionHash is available without full object overhead
```

### 3. Event Handler Usage Analysis

The system has two main event handler implementations:

#### 3.1 BcEventListenApplication (bcclient-stream)
**Configuration**: Used in `BCClientStreamConfig` for MainWebSocketConnectionPool
**Usage Pattern**:
```java
@Override
public void onEvent(WebSocketConnectionPoolBase connectionPool, EthBlock ethBlock) {
    Web3j web3j = connectionPool.getWebSocketConnection();
    
    List<EthBlock.TransactionResult> results = ethBlock.getResult().getTransactions();
    for (EthBlock.TransactionResult result : results) {
        this.handleTransaction(web3j, (EthBlock.TransactionObject) result); // ← Casts to full object
    }
}

void handleTransaction(Web3j web3j, EthBlock.TransactionObject transactionObj) {
    String transactionHash = transactionObj.getHash(); // ← ONLY USES HASH
    
    // Rest of processing uses only the hash for:
    // - ethGetTransactionReceipt(transactionHash)
    // - dynamoDbAdaptor.existsTransactionWithHash(transactionHash)
    // - dynamoDbAdaptor.completeTransactionStatus(transactionHash, ...)
}
```

**Evidence**: Only `transactionObj.getHash()` is accessed from the full transaction object.

#### 3.2 WakeUpHandler (bcclient)
**Configuration**: Used in `BCClientConfig` for MainWebSocketConnectionPool
**Usage Pattern**:
```java
@Override
public void onEvent(WebSocketConnectionPoolBase connectionPool, EthBlock ethBlock) {
    this.waitingTreads.forEachKey(1, key -> {
        Thread thread = this.waitingTreads.remove(key);
        if (thread == null) {
            return;
        }
        LockSupport.unpark(thread);
    });
}
```

**Evidence**: Does NOT use transaction data at all - only uses the block event as a trigger.

#### 3.3 SubWebSocketConnectionPool
**Configuration**: Uses `BcEventHandler.NO_ACTION`
**Usage Pattern**: No processing of transaction data.

## Performance Impact Analysis

### 1. Data Transfer Overhead

#### Full Transaction Object (current - parameter = true)
```json
{
  "hash": "0x...",
  "nonce": "0x...",
  "blockHash": "0x...",
  "blockNumber": "0x...",
  "transactionIndex": "0x...",
  "from": "0x...",
  "to": "0x...",
  "value": "0x...",
  "gasPrice": "0x...",
  "gas": "0x...",
  "input": "0x...",
  "v": "0x...",
  "r": "0x...",
  "s": "0x..."
}
```
**Estimated size per transaction**: ~500-2000 bytes (depending on input data)

#### Transaction Hash Only (optimized - parameter = false)
```json
"0x1234567890abcdef..."
```
**Estimated size per transaction**: ~66 bytes (32-byte hash + JSON overhead)

#### Savings Calculation
- **Per transaction savings**: ~434-1934 bytes (87-97% reduction)
- **Per block savings**: (savings per tx) × (transactions per block)
- **For 100 tx/block**: ~43KB-193KB per block
- **For high-activity periods**: Significant bandwidth and parsing time reduction

### 2. Processing Overhead

#### Current Processing (Full Objects)
1. JSON deserialization of complete transaction objects
2. Memory allocation for all transaction fields
3. Object casting from TransactionResult to TransactionObject
4. Field access to extract only the hash

#### Optimized Processing (Hash Only)
1. JSON deserialization of hash strings only
2. Minimal memory allocation
3. Direct hash string access
4. No unnecessary field processing

## Recommended Solution

### 1. Code Change

**File**: `WebSocketConnectionPoolBase.java`
**Line**: 162

```java
// BEFORE (current)
ethBlock = this.callerWeb3j.ethGetBlockByNumber(parameter, true).send();

// AFTER (optimized)
ethBlock = this.callerWeb3j.ethGetBlockByNumber(parameter, false).send();
```

### 2. Handler Adaptation Required

**File**: `BcEventListenApplication.java`
**Method**: `onEvent` and `handleTransaction`

```java
// BEFORE (current)
@Override
public void onEvent(WebSocketConnectionPoolBase connectionPool, EthBlock ethBlock) {
    Web3j web3j = connectionPool.getWebSocketConnection();
    
    List<EthBlock.TransactionResult> results = ethBlock.getResult().getTransactions();
    for (EthBlock.TransactionResult result : results) {
        this.handleTransaction(web3j, (EthBlock.TransactionObject) result);
    }
}

void handleTransaction(Web3j web3j, EthBlock.TransactionObject transactionObj) {
    String transactionHash = transactionObj.getHash();
    // ... rest of processing
}

// AFTER (optimized)
@Override
public void onEvent(WebSocketConnectionPoolBase connectionPool, EthBlock ethBlock) {
    Web3j web3j = connectionPool.getWebSocketConnection();
    
    List<EthBlock.TransactionResult> results = ethBlock.getResult().getTransactions();
    for (EthBlock.TransactionResult result : results) {
        String transactionHash = (String) result.get(); // Direct hash access
        this.handleTransaction(web3j, transactionHash);
    }
}

void handleTransaction(Web3j web3j, String transactionHash) {
    // ... same processing logic, but with hash parameter directly
}
```

## Benefits

### 1. Performance Improvements
- **Reduced bandwidth usage**: 87-97% reduction in transaction data transfer
- **Faster JSON parsing**: Minimal deserialization overhead
- **Lower memory consumption**: No allocation of unused transaction fields
- **Improved response times**: Faster block processing

### 2. Scalability Benefits
- **Better handling of high-transaction blocks**: Reduced processing time per block
- **Lower network congestion**: Reduced data transfer requirements
- **Improved system responsiveness**: Faster event processing pipeline

### 3. Cost Optimization
- **Reduced network costs**: Lower bandwidth usage
- **Improved resource utilization**: Less CPU and memory usage
- **Better throughput**: Higher block processing capacity

## Risk Assessment

### 1. Low Risk
- **Minimal code changes**: Only affects parameter value and handler adaptation
- **No functional impact**: Same transaction hashes are available
- **Backward compatible**: No breaking changes to external interfaces

### 2. Testing Requirements
- **Unit tests**: Verify hash extraction works correctly
- **Integration tests**: Ensure event processing pipeline functions properly
- **Performance tests**: Validate improvement metrics

## Implementation Priority

**Priority**: High
**Effort**: Low
**Impact**: High

This optimization provides significant performance benefits with minimal implementation effort and risk.
